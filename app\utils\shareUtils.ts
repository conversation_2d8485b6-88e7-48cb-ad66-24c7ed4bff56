import { TournamentDetails } from "@/app/types/CommonComponent.types";

/**
 * Generates a shareable URL for a tournament
 * @param tournamentDetails - The tournament details object
 * @param tournamentType - The type of tournament (e.g., "hybrid", "standard", etc.)
 * @param baseUrl - The base URL of the application (optional, defaults to current origin)
 * @returns A shareable URL string
 */
export const generateTournamentShareUrl = (
  tournamentDetails: TournamentDetails | null,
  tournamentType: string = "hybrid",
  baseUrl?: string
): string => {
  if (!tournamentDetails) {
    return baseUrl || window.location.origin;
  }

  const origin = baseUrl || window.location.origin;
  
  // Create URL parameters for the game details page
  const params = new URLSearchParams();
  
  // Add tournament type information
  params.set("type", tournamentType);
  
  // Add tournament-specific information
  if (tournamentDetails.name) {
    params.set("game", tournamentDetails.name);
  }
  
  // Add tournament ID for direct access
  params.set("tournament_id", tournamentDetails.tournament_id);
  
  // Add additional context that might be useful
  if (tournamentDetails.map) {
    params.set("map", tournamentDetails.map);
  }
  
  if (tournamentDetails.mode) {
    params.set("mode", tournamentDetails.mode);
  }

  // Construct the final URL
  const shareUrl = `${origin}/game-details?${params.toString()}`;
  
  return shareUrl;
};

/**
 * Generates share text for social sharing
 * @param tournamentDetails - The tournament details object
 * @returns An object with title and text for sharing
 */
export const generateShareContent = (
  tournamentDetails: TournamentDetails | null
): { title: string; text: string } => {
  if (!tournamentDetails) {
    return {
      title: "Join GameDay Tournament!",
      text: "Check out this exciting gaming tournament on GameDay!",
    };
  }

  const prizePool = tournamentDetails.prize_pool 
    ? `₹${tournamentDetails.prize_pool.toLocaleString("en-IN")}` 
    : "Amazing prizes";
    
  const joinFee = tournamentDetails.payment?.final_amount 
    ? `₹${tournamentDetails.payment.final_amount.toLocaleString("en-IN")}` 
    : "Low entry fee";

  const title = `${tournamentDetails.name || "Gaming Tournament"} - ${prizePool} Prize Pool!`;
  
  const text = `🎮 Join this exciting ${tournamentDetails.name || "gaming"} tournament!\n` +
    `💰 Prize Pool: ${prizePool}\n` +
    `🎯 Entry Fee: ${joinFee}\n` +
    `📅 Date: ${tournamentDetails.date}\n` +
    `🗺️ Map: ${tournamentDetails.map || "TBA"}\n` +
    `🎯 Mode: ${tournamentDetails.mode || "TBA"}\n\n` +
    `Join now on GameDay!`;

  return { title, text };
};

/**
 * Checks if the current environment supports native sharing
 * @returns boolean indicating if Web Share API is available
 */
export const isNativeSharingSupported = (): boolean => {
  return typeof navigator !== "undefined" && 
         "share" in navigator && 
         "canShare" in navigator;
};

/**
 * Checks if clipboard API is available
 * @returns boolean indicating if clipboard API is available
 */
export const isClipboardSupported = (): boolean => {
  return typeof navigator !== "undefined" && 
         "clipboard" in navigator && 
         "writeText" in navigator.clipboard;
};

/**
 * Validates if a share data object can be shared
 * @param shareData - The data to be shared
 * @returns boolean indicating if the data can be shared
 */
export const canShareData = (shareData: ShareData): boolean => {
  if (!isNativeSharingSupported()) {
    return false;
  }
  
  try {
    return navigator.canShare(shareData);
  } catch {
    return false;
  }
};

/**
 * Creates a fallback share method using various social platforms
 * @param url - The URL to share
 * @param title - The title for sharing
 * @param text - The text content for sharing
 * @returns An object with different platform share URLs
 */
export const createSocialShareUrls = (
  url: string, 
  title: string, 
  text: string
) => {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedText = encodeURIComponent(text);
  
  return {
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedText}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    whatsapp: `https://wa.me/?text=${encodedText}%20${encodedUrl}`,
    telegram: `https://t.me/share/url?url=${encodedUrl}&text=${encodedText}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
  };
};
